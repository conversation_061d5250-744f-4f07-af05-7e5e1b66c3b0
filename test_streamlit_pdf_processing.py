#!/usr/bin/env python3
"""
Test script to verify that the Streamlit app can process real PDF files correctly.
"""

import sys
from pathlib import Path
import io
from PyPDF2 import PdfReader

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_pdf_extraction():
    """Test PDF text extraction with our test files."""
    print("🧪 Testing PDF Text Extraction")
    print("=" * 50)
    
    test_files = list(Path('test_resumes').glob('*.pdf'))
    
    if not test_files:
        print("❌ No test PDF files found!")
        return False
    
    print(f"📁 Found {len(test_files)} test PDF files")
    
    successful_extractions = 0
    
    for pdf_file in test_files:
        print(f"\n📄 Testing: {pdf_file.name}")
        
        try:
            with open(pdf_file, 'rb') as f:
                pdf_content = f.read()
            
            # Extract text using PyPDF2 (same method as Streamlit app)
            pdf_reader = PdfReader(io.BytesIO(pdf_content))
            extracted_text = ""
            for page in pdf_reader.pages:
                extracted_text += page.extract_text() + "\n"
            
            if extracted_text and len(extracted_text.strip()) > 100:
                successful_extractions += 1
                print(f"   ✅ SUCCESS: Extracted {len(extracted_text)} characters")
                print(f"   📝 Preview: {extracted_text[:100]}...")
            else:
                print(f"   ❌ FAILED: Insufficient text extracted ({len(extracted_text)} chars)")
                
        except Exception as e:
            print(f"   ❌ ERROR: {e}")
    
    print(f"\n📊 Results: {successful_extractions}/{len(test_files)} files processed successfully")
    
    if successful_extractions == len(test_files):
        print("🎉 All PDF files can be processed correctly!")
        return True
    else:
        print("⚠️  Some PDF files failed to process")
        return False

def test_streamlit_import():
    """Test that we can import the updated Streamlit app."""
    print("\n🔧 Testing Streamlit App Import")
    print("=" * 50)
    
    try:
        import streamlit_app
        print("✅ Streamlit app imported successfully")
        
        # Check if PyPDF2 is available in the app
        if hasattr(streamlit_app, 'PdfReader'):
            print("✅ PyPDF2 PdfReader is available in streamlit_app")
        else:
            print("⚠️  PyPDF2 PdfReader not directly accessible, but should work via imports")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to import streamlit_app: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing CV Analyzer PDF Processing")
    print("=" * 60)
    
    # Test PDF extraction
    pdf_test_passed = test_pdf_extraction()
    
    # Test Streamlit import
    streamlit_test_passed = test_streamlit_import()
    
    print("\n" + "=" * 60)
    print("📋 FINAL RESULTS")
    print("=" * 60)
    
    if pdf_test_passed and streamlit_test_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The CV Analyzer can process real PDF files correctly")
        print("✅ The Streamlit app is ready for real-world use")
        print("\n🚀 You can now:")
        print("   1. Run: streamlit run streamlit_app.py")
        print("   2. Upload the test PDF files from test_resumes/")
        print("   3. See real CV analysis results!")
    else:
        print("❌ Some tests failed")
        if not pdf_test_passed:
            print("   - PDF extraction needs attention")
        if not streamlit_test_passed:
            print("   - Streamlit app import needs attention")
