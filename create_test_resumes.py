#!/usr/bin/env python3
"""
Create test PDF resumes for validating the CV analyzer functionality.
"""

from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from datetime import datetime
import os

def create_resume_pdf(filename, resume_data):
    """Create a PDF resume from structured data."""
    doc = SimpleDocTemplate(filename, pagesize=letter, topMargin=0.5*inch)
    styles = getSampleStyleSheet()
    story = []
    
    # Custom styles
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=12,
        alignment=1  # Center alignment
    )
    
    heading_style = ParagraphStyle(
        'CustomHeading',
        parent=styles['Heading2'],
        fontSize=14,
        spaceAfter=6,
        textColor=colors.darkblue
    )
    
    # Name and contact info
    story.append(Paragraph(resume_data['name'], title_style))
    story.append(Paragraph(resume_data['contact'], styles['Normal']))
    story.append(Spacer(1, 12))
    
    # Professional Summary
    if 'summary' in resume_data:
        story.append(Paragraph("PROFESSIONAL SUMMARY", heading_style))
        story.append(Paragraph(resume_data['summary'], styles['Normal']))
        story.append(Spacer(1, 12))
    
    # Experience
    story.append(Paragraph("PROFESSIONAL EXPERIENCE", heading_style))
    for exp in resume_data['experience']:
        story.append(Paragraph(f"<b>{exp['title']}</b> | {exp['company']} | {exp['dates']}", styles['Normal']))
        for bullet in exp['bullets']:
            story.append(Paragraph(f"• {bullet}", styles['Normal']))
        story.append(Spacer(1, 6))
    
    # Skills
    story.append(Paragraph("TECHNICAL SKILLS", heading_style))
    story.append(Paragraph(resume_data['skills'], styles['Normal']))
    story.append(Spacer(1, 12))
    
    # Education
    story.append(Paragraph("EDUCATION", heading_style))
    for edu in resume_data['education']:
        story.append(Paragraph(f"<b>{edu['degree']}</b> | {edu['school']} | {edu['year']}", styles['Normal']))
    
    doc.build(story)
    print(f"✅ Created resume: {filename}")

def create_test_resumes():
    """Create a set of test resumes with different profiles."""
    
    # Create test_resumes directory
    os.makedirs('test_resumes', exist_ok=True)
    
    # Resume 1: Senior Python Developer (Strong match)
    resume1 = {
        'name': 'Sarah Johnson',
        'contact': '<EMAIL> | (555) 123-4567 | LinkedIn: /in/sarahjohnson',
        'summary': 'Senior Python Developer with 6+ years of experience building scalable web applications. Expert in Django, Flask, and cloud technologies. Proven track record of leading development teams and delivering high-quality software solutions.',
        'experience': [
            {
                'title': 'Senior Python Developer',
                'company': 'TechCorp Solutions',
                'dates': '2021 - Present',
                'bullets': [
                    'Lead development of microservices architecture using Python, Django, and Docker',
                    'Manage team of 4 junior developers and conduct code reviews',
                    'Implemented CI/CD pipelines reducing deployment time by 60%',
                    'Built RESTful APIs serving 1M+ requests daily with 99.9% uptime'
                ]
            },
            {
                'title': 'Python Developer',
                'company': 'StartupXYZ',
                'dates': '2019 - 2021',
                'bullets': [
                    'Developed web applications using Django and PostgreSQL',
                    'Integrated third-party APIs and payment processing systems',
                    'Optimized database queries improving performance by 40%',
                    'Collaborated with frontend team using React and REST APIs'
                ]
            },
            {
                'title': 'Junior Software Developer',
                'company': 'DevCorp Inc',
                'dates': '2018 - 2019',
                'bullets': [
                    'Built Python scripts for data processing and automation',
                    'Worked with Flask framework for web development',
                    'Participated in Agile development methodology',
                    'Gained experience with AWS cloud services'
                ]
            }
        ],
        'skills': 'Python (6+ years), Django, Flask, PostgreSQL, MySQL, AWS, Docker, Kubernetes, Git, CI/CD, REST APIs, Microservices, Team Leadership, Agile/Scrum',
        'education': [
            {
                'degree': 'Bachelor of Science in Computer Science',
                'school': 'University of Technology',
                'year': '2018'
            }
        ]
    }
    
    # Resume 2: Mid-level Developer (Good match with some gaps)
    resume2 = {
        'name': 'Michael Chen',
        'contact': '<EMAIL> | (555) 987-6543 | GitHub: /michaelchen',
        'summary': 'Full-stack developer with 4 years of experience in Python and JavaScript. Passionate about clean code and modern development practices. Experience with both backend and frontend technologies.',
        'experience': [
            {
                'title': 'Full Stack Developer',
                'company': 'WebSolutions Ltd',
                'dates': '2022 - Present',
                'bullets': [
                    'Develop web applications using Python Flask and React.js',
                    'Design and implement RESTful APIs for mobile applications',
                    'Work with PostgreSQL and MongoDB databases',
                    'Collaborate with UX/UI designers and product managers'
                ]
            },
            {
                'title': 'Software Developer',
                'company': 'InnovateTech',
                'dates': '2020 - 2022',
                'bullets': [
                    'Built backend services using Python and Django',
                    'Implemented automated testing with pytest and unittest',
                    'Worked with Redis for caching and session management',
                    'Deployed applications on AWS EC2 and RDS'
                ]
            },
            {
                'title': 'Career Break',
                'company': 'Personal Development',
                'dates': '2019 - 2020',
                'bullets': [
                    'Took time off for family reasons and skill development',
                    'Completed online courses in machine learning and data science',
                    'Contributed to open-source Python projects on GitHub',
                    'Maintained technical skills through personal projects'
                ]
            },
            {
                'title': 'Junior Developer',
                'company': 'CodeCraft Studios',
                'dates': '2018 - 2019',
                'bullets': [
                    'Developed Python scripts for data analysis and reporting',
                    'Learned web development with Flask framework',
                    'Worked with MySQL databases and SQL queries',
                    'Participated in code reviews and team meetings'
                ]
            }
        ],
        'skills': 'Python (4+ years), Django, Flask, JavaScript, React.js, PostgreSQL, MongoDB, Redis, AWS, Git, pytest, REST APIs, HTML/CSS',
        'education': [
            {
                'degree': 'Bachelor of Engineering in Software Engineering',
                'school': 'State University',
                'year': '2018'
            }
        ]
    }
    
    # Resume 3: Junior Developer (Partial match, less experience)
    resume3 = {
        'name': 'Emily Rodriguez',
        'contact': '<EMAIL> | (555) 456-7890 | Portfolio: emilydev.com',
        'summary': 'Recent computer science graduate with 2 years of internship experience in Python development. Eager to contribute to a dynamic development team and grow technical skills in a collaborative environment.',
        'experience': [
            {
                'title': 'Python Developer Intern',
                'company': 'DataTech Solutions',
                'dates': '2023 - Present',
                'bullets': [
                    'Develop data processing scripts using Python and pandas',
                    'Create simple web applications with Flask framework',
                    'Work with SQLite and basic database operations',
                    'Learn version control with Git and GitHub'
                ]
            },
            {
                'title': 'Software Development Intern',
                'company': 'LocalTech Startup',
                'dates': '2022 - 2023',
                'bullets': [
                    'Assisted in building web applications using Python',
                    'Learned basic Django framework concepts',
                    'Participated in daily standup meetings and sprint planning',
                    'Wrote unit tests for existing codebase'
                ]
            }
        ],
        'skills': 'Python (2+ years), Flask, Django (basic), SQLite, MySQL (basic), Git, pandas, HTML/CSS, JavaScript (basic)',
        'education': [
            {
                'degree': 'Bachelor of Science in Computer Science',
                'school': 'Community College of Technology',
                'year': '2023'
            }
        ]
    }
    
    # Resume 4: Career Changer (Red flags - frequent job changes)
    resume4 = {
        'name': 'David Thompson',
        'contact': '<EMAIL> | (555) 321-9876',
        'summary': 'Career changer transitioning from marketing to software development. Self-taught Python programmer with passion for technology and problem-solving.',
        'experience': [
            {
                'title': 'Junior Python Developer',
                'company': 'QuickCode Inc',
                'dates': '2023 - Present (6 months)',
                'bullets': [
                    'Learning Python development in fast-paced environment',
                    'Working on small web projects using Flask',
                    'Struggling with complex database relationships',
                    'Attending daily code review sessions'
                ]
            },
            {
                'title': 'Freelance Web Developer',
                'company': 'Self-Employed',
                'dates': '2022 - 2023',
                'bullets': [
                    'Built simple websites for local businesses',
                    'Used basic Python and HTML/CSS',
                    'Inconsistent project delivery due to learning curve',
                    'Limited experience with professional development practices'
                ]
            },
            {
                'title': 'Marketing Coordinator',
                'company': 'MarketPro Agency',
                'dates': '2020 - 2022',
                'bullets': [
                    'Managed social media campaigns and content creation',
                    'Analyzed marketing data using Excel and basic Python scripts',
                    'Coordinated with clients and internal teams',
                    'Decided to transition to software development'
                ]
            },
            {
                'title': 'Sales Representative',
                'company': 'RetailCorp',
                'dates': '2019 - 2020',
                'bullets': [
                    'Sold consumer electronics and technology products',
                    'Developed interest in technology and programming',
                    'Left due to lack of growth opportunities',
                    'Started learning programming in spare time'
                ]
            }
        ],
        'skills': 'Python (1+ year), Flask (basic), HTML/CSS, Excel, Marketing Analytics, Self-motivated learning',
        'education': [
            {
                'degree': 'Bachelor of Arts in Marketing',
                'school': 'Business University',
                'year': '2019'
            },
            {
                'degree': 'Python Programming Bootcamp',
                'school': 'CodeAcademy Online',
                'year': '2022'
            }
        ]
    }
    
    # Create the PDF files
    resumes = [
        ('test_resumes/sarah_johnson_senior_python.pdf', resume1),
        ('test_resumes/michael_chen_fullstack.pdf', resume2),
        ('test_resumes/emily_rodriguez_junior.pdf', resume3),
        ('test_resumes/david_thompson_career_changer.pdf', resume4)
    ]
    
    for filename, resume_data in resumes:
        create_resume_pdf(filename, resume_data)
    
    print(f"\n✅ Created {len(resumes)} test resumes in 'test_resumes/' directory")
    return [filename for filename, _ in resumes]

if __name__ == "__main__":
    create_test_resumes()
