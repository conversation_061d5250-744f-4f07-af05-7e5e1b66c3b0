#!/usr/bin/env python3
"""
Test script to validate that the session state initialization fix works correctly.
This script simulates the session state access patterns that were causing the error.
"""

import sys
import os
import logging
from unittest.mock import Mock, patch

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_session_state_initialization():
    """Test that session state variables are properly initialized."""
    
    print("🧪 Testing Session State Initialization Fix...")
    
    # Mock Streamlit session state
    class MockSessionState:
        def __init__(self):
            self._state = {}
        
        def __contains__(self, key):
            return key in self._state
        
        def __getattr__(self, key):
            if key.startswith('_'):
                return object.__getattribute__(self, key)
            if key not in self._state:
                raise AttributeError(f"st.session_state has no attribute '{key}'. Did you forget to initialize it?")
            return self._state[key]
        
        def __setattr__(self, key, value):
            if key.startswith('_'):
                object.__setattr__(self, key, value)
            else:
                self._state[key] = value
        
        def __getitem__(self, key):
            return self._state[key]
        
        def __setitem__(self, key, value):
            self._state[key] = value
    
    # Create mock session state
    mock_session_state = MockSessionState()
    
    # Mock streamlit module
    with patch('streamlit.session_state', mock_session_state):
        with patch('streamlit.spinner'), patch('streamlit.error'), patch('streamlit.stop'):
            # Import the modules that use session state
            try:
                from session_manager import session_manager
                from streamlit_app import initialize_session_state
                
                print("✅ Successfully imported modules")
                
                # Test the initialization function
                print("🔧 Testing initialize_session_state()...")
                initialize_session_state()
                print("✅ initialize_session_state() completed without errors")
                
                # Test that critical variables are initialized
                critical_vars = [
                    'processing_status',
                    'analysis_results', 
                    'export_data',
                    'system_initialized',
                    'integration_manager',
                    'mandatory_skills_text',
                    'preferred_skills_text',
                    'current_analysis_results',
                    'job_description',
                    'analysis_config'
                ]
                
                print("🔍 Checking critical session state variables...")
                for var in critical_vars:
                    if var in mock_session_state:
                        print(f"  ✅ {var}: {type(getattr(mock_session_state, var, None))}")
                    else:
                        print(f"  ❌ {var}: NOT INITIALIZED")
                
                # Test accessing processing_status (the original error)
                print("🎯 Testing processing_status access...")
                try:
                    status = mock_session_state.processing_status
                    print(f"✅ processing_status accessed successfully: {status}")
                except AttributeError as e:
                    print(f"❌ processing_status access failed: {e}")
                    return False
                
                # Test setting processing_status
                print("🎯 Testing processing_status assignment...")
                try:
                    mock_session_state.processing_status = {'is_processing': True}
                    print(f"✅ processing_status set successfully: {mock_session_state.processing_status}")
                except Exception as e:
                    print(f"❌ processing_status assignment failed: {e}")
                    return False
                
                print("🎉 All session state tests passed!")
                return True
                
            except Exception as e:
                print(f"❌ Error during testing: {e}")
                import traceback
                traceback.print_exc()
                return False

def test_render_processing_status():
    """Test that render_processing_status function works with initialized state."""
    
    print("\n🧪 Testing render_processing_status function...")
    
    # Mock Streamlit functions
    with patch('streamlit.session_state') as mock_st_state:
        with patch('streamlit.markdown'), patch('streamlit.progress'), patch('streamlit.write'):
            
            # Test with None processing_status (should not crash)
            mock_st_state.processing_status = None
            
            try:
                from streamlit_app import render_processing_status
                render_processing_status()
                print("✅ render_processing_status() with None status - OK")
            except Exception as e:
                print(f"❌ render_processing_status() with None status failed: {e}")
                return False
            
            # Test with processing status data
            mock_st_state.processing_status = {
                'is_processing': True,
                'progress': {
                    'progress_percentage': 50,
                    'current_step': 'Analyzing CVs',
                    'total_steps': 4,
                    'completed_steps': 2
                }
            }
            
            try:
                render_processing_status()
                print("✅ render_processing_status() with progress data - OK")
            except Exception as e:
                print(f"❌ render_processing_status() with progress data failed: {e}")
                return False
            
            print("🎉 render_processing_status tests passed!")
            return True

if __name__ == "__main__":
    print("🚀 Starting Session State Fix Validation\n")
    
    success1 = test_session_state_initialization()
    success2 = test_render_processing_status()
    
    if success1 and success2:
        print("\n🎉 ALL TESTS PASSED! Session state fix is working correctly.")
        print("✅ The 'st.session_state has no attribute processing_status' error should be resolved.")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! Please check the implementation.")
        sys.exit(1)
