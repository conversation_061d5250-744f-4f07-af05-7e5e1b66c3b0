{"timestamp": "2025-07-30T14:31:24.021473", "overall_success": true, "test_summary": {"System Initialization": true, "PDF Processing": true, "CV Analysis": true, "Export Functionality": true}, "detailed_results": [{"test": "Integration Manager I<PERSON>rt", "success": true, "details": "", "timestamp": "2025-07-30T14:30:40.613069"}, {"test": "System Initialization", "success": true, "details": "Initialized: True", "timestamp": "2025-07-30T14:30:40.716175"}, {"test": "Component Loading", "success": true, "details": "Loaded 9 components", "timestamp": "2025-07-30T14:30:40.716222"}, {"test": "API Key Configuration", "success": true, "details": "", "timestamp": "2025-07-30T14:30:40.716227"}, {"test": "PDF Files Available", "success": true, "details": "Found 4 test files", "timestamp": "2025-07-30T14:30:40.716644"}, {"test": "PDF Extraction - em<PERSON>_<PERSON><PERSON><PERSON><PERSON>_junior.pdf", "success": true, "details": "Extracted 1075 characters", "timestamp": "2025-07-30T14:30:40.724923"}, {"test": "PDF Extraction - mi<PERSON><PERSON>_chen_fullstack.pdf", "success": true, "details": "Extracted 1601 characters", "timestamp": "2025-07-30T14:30:40.733192"}, {"test": "PDF Extraction - david_thompson_career_changer.pdf", "success": true, "details": "Extracted 1497 characters", "timestamp": "2025-07-30T14:30:40.736759"}, {"test": "PDF Extraction - <PERSON><PERSON>_johnson_senior_python.pdf", "success": true, "details": "Extracted 1479 characters", "timestamp": "2025-07-30T14:30:40.741037"}, {"test": "Overall PDF Processing", "success": true, "details": "4/4 successful", "timestamp": "2025-07-30T14:30:40.741062"}, {"test": "CV Data Preparation", "success": true, "details": "Prepared 4 CVs", "timestamp": "2025-07-30T14:30:40.757469"}, {"test": "Analysis Execution", "success": true, "details": "Processed 4 CVs", "timestamp": "2025-07-30T14:31:24.021008"}, {"test": "Successful Processing", "success": true, "details": "4/4 successful", "timestamp": "2025-07-30T14:31:24.021041"}, {"test": "Assessment - em<PERSON>_<PERSON><PERSON><PERSON><PERSON>_junior.pdf", "success": true, "details": "Match: 60.0%, Red flags: 0", "timestamp": "2025-07-30T14:31:24.021070"}, {"test": "Assessment - mi<PERSON><PERSON>_chen_fullstack.pdf", "success": true, "details": "Match: 80.0%, Red flags: 0", "timestamp": "2025-07-30T14:31:24.021086"}, {"test": "Assessment - david_thompson_career_changer.pdf", "success": true, "details": "Match: 20.0%, Red flags: 0", "timestamp": "2025-07-30T14:31:24.021098"}, {"test": "Assessment - sa<PERSON>_johnson_senior_python.pdf", "success": true, "details": "Match: 90.0%, Red flags: 0", "timestamp": "2025-07-30T14:31:24.021109"}, {"test": "Export Service Creation", "success": true, "details": "", "timestamp": "2025-07-30T14:31:24.021166"}, {"test": "Export Method - export_to_excel", "success": false, "details": "", "timestamp": "2025-07-30T14:31:24.021177"}, {"test": "Export Method - export_to_csv", "success": false, "details": "", "timestamp": "2025-07-30T14:31:24.021186"}, {"test": "Export Method - export_to_json", "success": false, "details": "", "timestamp": "2025-07-30T14:31:24.021229"}], "stats": {"total_tests": 21, "passed": 18, "failed": 3, "success_rate": 85.71428571428571}}