2025-07-28 20:49:43,006 - perf.test_async_function - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:43.006041", "logger": "perf.test_async_function", "level": "info", "message": "Operation completed: test_operation", "duration": 0.01067209243774414}
2025-07-28 20:49:43,018 - perf.test_async_function - ERROR - _log_structured:187 - {"timestamp": "2025-07-28T20:49:43.018352", "logger": "perf.test_async_function", "level": "error", "message": "Operation failed: test_operation_fail", "error": "Test error", "duration": 3.695487976074219e-05}
2025-07-28 20:51:21,629 - perf.test_async_function - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.629590", "logger": "perf.test_async_function", "level": "info", "message": "Operation completed: test_operation", "duration": 0.010507822036743164}
2025-07-28 20:51:21,637 - perf.test_async_function - ERROR - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.637773", "logger": "perf.test_async_function", "level": "error", "message": "Operation failed: test_operation_fail", "error": "Test error", "duration": 2.7418136596679688e-05}
2025-07-28 20:51:57,733 - perf.test_async_function - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.733776", "logger": "perf.test_async_function", "level": "info", "message": "Operation completed: test_operation", "duration": 0.010575294494628906}
2025-07-28 20:51:57,742 - perf.test_async_function - ERROR - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.742064", "logger": "perf.test_async_function", "level": "error", "message": "Operation failed: test_operation_fail", "error": "Test error", "duration": 2.7894973754882812e-05}
