2025-07-28 20:49:42,923 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.923703", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,938 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.937931", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,938 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.938692", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "processing", "progress": 40.0, "current_step": "Processing PDF", "completed_steps": 2, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,943 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.943635", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,944 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.944328", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "completed", "progress": 100.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,958 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.958380", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,959 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.959161", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "failed", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": "Processing error", "metadata": {}}
2025-07-28 20:49:42,968 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.967061", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,969 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.969310", "logger": "status_tracker", "level": "info", "message": "Processing status: skill_analysis", "operation_id": "test_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,978 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.978407", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,979 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.979531", "logger": "status_tracker", "level": "info", "message": "Processing status: skill_analysis", "operation_id": "test_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:42,980 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:42.980276", "logger": "status_tracker", "level": "info", "message": "Cleaned up 1 old processing statuses"}
2025-07-28 20:49:43,041 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:43.041069", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 3, "error_message": null, "metadata": {}}
2025-07-28 20:49:43,041 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:43.041795", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_123", "status": "completed", "progress": 100.0, "current_step": "", "completed_steps": 0, "total_steps": 3, "error_message": null, "metadata": {}}
2025-07-28 20:49:43,046 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:43.046204", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:49:43,046 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:49:43.046799", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_456", "status": "failed", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": "Test error", "metadata": {}}
2025-07-28 20:51:21,575 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.575243", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,581 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.581464", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,582 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.582194", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "processing", "progress": 40.0, "current_step": "Processing PDF", "completed_steps": 2, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,587 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.587323", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,588 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.587934", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "completed", "progress": 100.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,595 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.595166", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,595 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.595756", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "failed", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": "Processing error", "metadata": {}}
2025-07-28 20:51:21,601 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.601656", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,602 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.602391", "logger": "status_tracker", "level": "info", "message": "Processing status: skill_analysis", "operation_id": "test_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,608 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.608141", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,609 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.608906", "logger": "status_tracker", "level": "info", "message": "Processing status: skill_analysis", "operation_id": "test_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,609 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.609275", "logger": "status_tracker", "level": "info", "message": "Cleaned up 1 old processing statuses"}
2025-07-28 20:51:21,658 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.658164", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 3, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,658 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.658703", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_123", "status": "completed", "progress": 100.0, "current_step": "", "completed_steps": 0, "total_steps": 3, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,661 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.661307", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:21,661 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:21.661600", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_456", "status": "failed", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": "Test error", "metadata": {}}
2025-07-28 20:51:57,679 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.678903", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,683 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.683344", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,685 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.685533", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "processing", "progress": 40.0, "current_step": "Processing PDF", "completed_steps": 2, "total_steps": 5, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,693 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.693110", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,694 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.694222", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "completed", "progress": 100.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,705 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.704885", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,706 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.706007", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "failed", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": "Processing error", "metadata": {}}
2025-07-28 20:51:57,713 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.712815", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,713 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.713708", "logger": "status_tracker", "level": "info", "message": "Processing status: skill_analysis", "operation_id": "test_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,719 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.719859", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_analysis", "operation_id": "test_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,720 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.720313", "logger": "status_tracker", "level": "info", "message": "Processing status: skill_analysis", "operation_id": "test_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,720 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.720728", "logger": "status_tracker", "level": "info", "message": "Cleaned up 1 old processing statuses"}
2025-07-28 20:51:57,764 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.764071", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_123", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 3, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,764 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.764804", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_123", "status": "completed", "progress": 100.0, "current_step": "", "completed_steps": 0, "total_steps": 3, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,769 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.769794", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_456", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": null, "metadata": {}}
2025-07-28 20:51:57,771 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:51:57.770977", "logger": "status_tracker", "level": "info", "message": "Processing status: test_operation", "operation_id": "test_context_456", "status": "failed", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 0, "error_message": "Test error", "metadata": {}}
2025-07-28 20:52:34,609 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.609125", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_batch_processing", "operation_id": "batch_001", "status": "pending", "progress": 0.0, "current_step": "", "completed_steps": 0, "total_steps": 3, "error_message": null, "metadata": {}}
2025-07-28 20:52:34,613 - status_tracker - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.613294", "logger": "status_tracker", "level": "info", "message": "Processing status: cv_batch_processing", "operation_id": "batch_001", "status": "completed", "progress": 100.0, "current_step": "Processing cv2.pdf", "completed_steps": 2, "total_steps": 3, "error_message": null, "metadata": {}}
