2025-07-28 20:52:34,610 - example_app - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.610543", "logger": "example_app", "level": "info", "message": "Logging system initialized", "logfire_enabled": false, "file_logging_enabled": true}
2025-07-28 20:52:34,610 - example_app - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.610912", "logger": "example_app", "level": "info", "message": "Successfully processed cv1.pdf", "file_size": 1024}
2025-07-28 20:52:34,611 - example_app - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.611717", "logger": "example_app", "level": "info", "message": "Logging system initialized", "logfire_enabled": false, "file_logging_enabled": true}
2025-07-28 20:52:34,611 - example_app - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.611948", "logger": "example_app", "level": "info", "message": "Successfully processed cv2.pdf", "file_size": 1024}
2025-07-28 20:52:34,612 - example_app - INFO - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.612479", "logger": "example_app", "level": "info", "message": "Logging system initialized", "logfire_enabled": false, "file_logging_enabled": true}
2025-07-28 20:52:34,612 - example_app - WARNING - _log_error:341 - [pdf_processing] Failed to extract text from PDF file for file 'cv3.pdf'. Please try again or contact support if the issue persists. | operation=cv_processing, file=cv3.pdf, component=example_processor
2025-07-28 20:52:34,612 - example_app - INFO - _recover_pdf_processing:348 - Attempting alternative PDF extraction for cv3.pdf
2025-07-28 20:52:34,612 - example_app - ERROR - _log_structured:187 - {"timestamp": "2025-07-28T20:52:34.612920", "logger": "example_app", "level": "error", "message": "Processing failed for cv3.pdf", "error": "Failed to extract text from PDF file for file 'cv3.pdf'. Please try again or contact support if the issue persists."}
