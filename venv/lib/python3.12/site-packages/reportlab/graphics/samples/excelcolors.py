# define standard colors to mimic those used by Microsoft Excel
from reportlab.lib.colors import PCMYKColor

#colour names as comments at the end of each line are as a memory jogger ONLY
#NOT HTML named colours!

#Main colours as used for bars etc
color01 = PCMYKColor(40,40,0,0)    # Lavender
color02 = PCMYKColor(0,66,33,39)   # Maroon
color03 = PCMYKColor(0,0,20,0)     # Yellow
color04 = PCMYKColor(20,0,0,0)     # Cyan
color05 = PCMYKColor(0,100,0,59)   # Purple
color06 = PCMYKColor(0,49,49,0)    # Salmon
color07 = PCMYKColor(100,49,0,19)  # Blue
color08 = PCMYKColor(20,20,0,0)    # PaleLavender
color09 = PCMYKColor(100,100,0,49) # NavyBlue
color10 = PCMYKColor(0,100,0,0)    # Purple

#Highlight colors - eg for the tops of bars
color01Light = PCMYKColor(39,39,0,25)   # Light Lavender
color02Light = PCMYKColor(0,66,33,54)   # Light Maroon
color03Light = PCMYKColor(0,0,19,25)    # Light Yellow
color04Light = PCMYKColor(19,0,0,25)    # Light Cyan
color05Light = PCMYKColor(0,100,0,69)   # Light Purple
color06Light = PCMYKColor(0,49,49,25)   # Light Salmon
color07Light = PCMYKColor(100,49,0,39)  # Light Blue
color08Light = PCMYKColor(19,19,0,25)   # Light PaleLavender
color09Light = PCMYKColor(100,100,0,62) # Light NavyBlue
color10Light = PCMYKColor(0,100,0,25)   # Light Purple

#Lowlight colors - eg for the sides of bars
color01Dark = PCMYKColor(39,39,0,49)   # Dark Lavender
color02Dark = PCMYKColor(0,66,33,69)   # Dark Maroon
color03Dark = PCMYKColor(0,0,20,49)    # Dark Yellow
color04Dark = PCMYKColor(20,0,0,49)    # Dark Cyan
color05Dark = PCMYKColor(0,100,0,80)   # Dark Purple
color06Dark = PCMYKColor(0,50,50,49)   # Dark Salmon
color07Dark = PCMYKColor(100,50,0,59)  # Dark Blue
color08Dark = PCMYKColor(20,20,0,49)   # Dark PaleLavender
color09Dark = PCMYKColor(100,100,0,79) # Dark NavyBlue
color10Dark = PCMYKColor(0,100,0,49)   # Dark Purple

#for standard grey backgrounds
backgroundGrey = PCMYKColor(0,0,0,24)

