# Requirements Document

## Introduction

This feature enhances the existing CV analysis MVP by adding a user-friendly Streamlit web interface and implementing advanced analysis capabilities. The system will provide recruiters with a comprehensive tool to analyze candidate CVs against job descriptions, detect potential red flags, and generate detailed assessment reports with improved accuracy and insights.

## Requirements

### Requirement 1

**User Story:** As a recruiter, I want a web-based interface to upload CVs and job descriptions, so that I can easily analyze candidates without using command-line tools.

#### Acceptance Criteria

1. WHEN a user accesses the application THEN the system SHALL display a Streamlit web interface
2. WHEN a user uploads PDF files THEN the system SHALL accept multiple CV files simultaneously
3. WHEN a user enters a job description THEN the system SHALL provide a text area for job requirements input
4. WHEN a user clicks analyze THEN the system SHALL process all uploaded CVs and display results
5. IF no files are uploaded THEN the system SHALL display an error message requesting file upload

### Requirement 2

**User Story:** As a recruiter, I want to detect employment gaps and short-term positions in candidate CVs, so that I can identify potential red flags in their career history.

#### Acceptance Criteria

1. WHEN analyzing work experience THEN the system SHALL extract start and end dates from each position
2. W<PERSON><PERSON> calculating employment gaps THEN the system SHALL identify periods longer than 3 months without employment
3. <PERSON><PERSON><PERSON> detecting short positions THEN the system SHALL flag positions lasting less than 6 months
4. WHEN multiple short positions are found THEN the system SHALL raise a "job hopping" alert
5. IF date extraction fails THEN the system SHALL log the issue and continue analysis

### Requirement 3

**User Story:** As a recruiter, I want to verify that required skills are from professional experience rather than academic projects, so that I can assess practical competency accurately.

#### Acceptance Criteria

1. WHEN analyzing skills THEN the system SHALL categorize them as academic, professional, or certification-based
2. WHEN matching job requirements THEN the system SHALL prioritize professional experience over academic mentions
3. WHEN a required skill appears only in academic context THEN the system SHALL flag it as "academic only"
4. WHEN calculating match scores THEN the system SHALL weight professional skills higher than academic ones
5. IF skill context cannot be determined THEN the system SHALL mark it as "context unclear"

### Requirement 4

**User Story:** As a recruiter, I want to see which skills were used in recent positions, so that I can evaluate current competency and skill relevance.

#### Acceptance Criteria

1. WHEN analyzing recent experience THEN the system SHALL focus on the last 3 positions or 5 years
2. WHEN extracting skills from positions THEN the system SHALL map technologies to specific roles
3. WHEN displaying results THEN the system SHALL show skills timeline and recency
4. WHEN skills are outdated THEN the system SHALL flag technologies not used in recent positions
5. IF position details are insufficient THEN the system SHALL note limited skill extraction

### Requirement 5

**User Story:** As a recruiter, I want an interactive dashboard to view and compare candidate assessments, so that I can make informed hiring decisions efficiently.

#### Acceptance Criteria

1. WHEN analysis completes THEN the system SHALL display results in an organized dashboard
2. WHEN viewing candidates THEN the system SHALL show sortable comparison tables
3. WHEN selecting a candidate THEN the system SHALL display detailed individual assessment
4. WHEN exporting results THEN the system SHALL generate Excel reports with all analysis data
5. IF no candidates meet criteria THEN the system SHALL display appropriate messaging

### Requirement 6

**User Story:** As a recruiter, I want the system to handle errors gracefully and provide clear feedback, so that I can understand and resolve issues quickly.

#### Acceptance Criteria

1. WHEN PDF extraction fails THEN the system SHALL display specific error messages
2. WHEN API calls fail THEN the system SHALL retry with exponential backoff
3. WHEN analysis encounters errors THEN the system SHALL continue processing other CVs
4. WHEN displaying results THEN the system SHALL show processing status for each CV
5. IF critical errors occur THEN the system SHALL log details and provide user guidance

### Requirement 7

**User Story:** As a recruiter, I want to customize analysis parameters and job requirements, so that I can adapt the tool to different positions and hiring criteria.

#### Acceptance Criteria

1. WHEN setting up analysis THEN the system SHALL allow customization of match score weights
2. WHEN defining requirements THEN the system SHALL support mandatory vs preferred skills specification
3. WHEN configuring alerts THEN the system SHALL allow adjustment of gap detection thresholds
4. WHEN saving settings THEN the system SHALL persist user preferences for future sessions
5. IF invalid parameters are entered THEN the system SHALL validate and provide correction guidance