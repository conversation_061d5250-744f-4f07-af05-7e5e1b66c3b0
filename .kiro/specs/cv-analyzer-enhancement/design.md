# Design Document

## Overview

The CV Analyzer Enhancement transforms the existing command-line MVP into a comprehensive web-based recruitment tool. The system maintains the core LLM-powered analysis while adding a Streamlit frontend, advanced career analysis features, and enhanced data processing capabilities. The architecture follows a modular design with clear separation between UI, business logic, and data processing layers.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    UI[Streamlit Frontend] --> API[Analysis Engine]
    API --> PDF[PDF Processor]
    API --> LLM[LLM Service]
    API --> ANALYZER[Career Analyzer]
    API --> EXPORT[Export Service]
    
    PDF --> CACHE[File Cache]
    LLM --> GROQ[Groq API]
    ANALYZER --> DATES[Date Parser]
    ANALYZER --> SKILLS[Skill Extractor]
    EXPORT --> EXCEL[Excel Generator]
    
    subgraph "Data Models"
        MODELS[Enhanced Models]
    end
    
    API --> MODELS
```

### Component Architecture

The system is organized into distinct layers:

1. **Presentation Layer**: Streamlit web interface
2. **Service Layer**: Core analysis and processing services
3. **Data Layer**: Enhanced data models and persistence
4. **Integration Layer**: External API connections and file handling

## Components and Interfaces

### 1. Streamlit Frontend (`streamlit_app.py`)

**Purpose**: Provides the web interface for CV analysis

**Key Components**:
- File upload widget for multiple PDF handling
- Job description input with rich text editor
- Configuration panel for analysis parameters
- Results dashboard with interactive tables and charts
- Export functionality

**Interface**:
```python
class StreamlitApp:
    def render_upload_section() -> List[UploadedFile]
    def render_job_description_input() -> str
    def render_configuration_panel() -> AnalysisConfig
    def render_results_dashboard(assessments: List[EnhancedAssessment]) -> None
    def handle_export(assessments: List[EnhancedAssessment]) -> None
```

### 2. Enhanced Analysis Engine (`analysis_engine.py`)

**Purpose**: Orchestrates the complete CV analysis workflow

**Key Components**:
- Async processing pipeline
- Error handling and retry logic
- Progress tracking
- Result aggregation

**Interface**:
```python
class AnalysisEngine:
    async def analyze_candidates(
        cvs: List[UploadedFile], 
        job_description: str, 
        config: AnalysisConfig
    ) -> List[EnhancedAssessment]
    
    async def process_single_cv(
        cv_file: UploadedFile, 
        job_description: str
    ) -> EnhancedAssessment
```

### 3. Career Analyzer (`career_analyzer.py`)

**Purpose**: Implements advanced career analysis features

**Key Components**:
- Employment gap detection
- Job hopping analysis
- Skill context classification
- Experience timeline construction

**Interface**:
```python
class CareerAnalyzer:
    def detect_employment_gaps(positions: List[Position]) -> List[EmploymentGap]
    def analyze_job_stability(positions: List[Position]) -> JobStabilityAnalysis
    def classify_skill_context(skills: List[str], cv_text: str) -> Dict[str, SkillContext]
    def build_experience_timeline(positions: List[Position]) -> ExperienceTimeline
```

### 4. Enhanced LLM Service (`llm_service.py`)

**Purpose**: Manages LLM interactions with improved prompting and error handling

**Key Components**:
- Structured prompting system
- Response validation
- Retry mechanism with exponential backoff
- Token usage optimization

**Interface**:
```python
class EnhancedLLMService:
    async def analyze_cv(cv_text: str, job_description: str) -> CandidateAssessment
    async def extract_positions(cv_text: str) -> List[Position]
    async def classify_skills(cv_text: str, skills: List[str]) -> Dict[str, SkillClassification]
    def validate_response(response: str) -> bool
```

### 5. Date Parser (`date_parser.py`)

**Purpose**: Extracts and normalizes dates from CV text

**Key Components**:
- Multiple date format recognition
- Fuzzy date matching
- Date range validation
- Current position detection

**Interface**:
```python
class DateParser:
    def extract_dates_from_text(text: str) -> List[DateRange]
    def normalize_date(date_str: str) -> Optional[datetime]
    def calculate_duration(start: datetime, end: Optional[datetime]) -> timedelta
    def detect_current_position(text: str) -> bool
```

## Data Models

### Enhanced Assessment Model

```python
class EnhancedAssessment(BaseModel):
    # Original fields
    candidate_name: str | None = None
    contact_phone: str | None = None
    address: str | None = None
    email: str | None = None
    github: str | None = None
    linkedin: str | None = None
    age: int | None = None
    current_position: str | None = None
    match_score: float = Field(ge=0.0, le=1.0)
    matched_sections: List[str]
    experience_years: int
    certifications: List[str]
    summary: str
    
    # Enhanced fields
    positions: List[Position]
    employment_gaps: List[EmploymentGap]
    job_stability_score: float
    skill_analysis: SkillAnalysis
    red_flags: List[RedFlag]
    processing_status: ProcessingStatus
    analysis_timestamp: datetime
```

### Supporting Models

```python
class Position(BaseModel):
    title: str
    company: str
    start_date: datetime | None
    end_date: datetime | None
    duration_months: int | None
    is_current: bool
    description: str
    skills_used: List[str]

class EmploymentGap(BaseModel):
    start_date: datetime
    end_date: datetime
    duration_months: int
    severity: GapSeverity

class SkillAnalysis(BaseModel):
    professional_skills: List[str]
    academic_skills: List[str]
    certification_skills: List[str]
    recent_skills: List[str]
    outdated_skills: List[str]
    skill_timeline: Dict[str, List[str]]

class RedFlag(BaseModel):
    type: RedFlagType
    severity: Severity
    description: str
    details: Dict[str, Any]

class AnalysisConfig(BaseModel):
    gap_threshold_months: int = 3
    short_position_threshold_months: int = 6
    job_hopping_threshold: int = 3
    recent_experience_years: int = 5
    skill_weights: Dict[str, float]
    mandatory_skills: List[str]
    preferred_skills: List[str]
```

## Error Handling

### Error Categories

1. **PDF Processing Errors**: Corrupted files, unsupported formats
2. **LLM API Errors**: Rate limits, network issues, invalid responses
3. **Data Validation Errors**: Malformed data, missing required fields
4. **Analysis Errors**: Date parsing failures, skill extraction issues

### Error Handling Strategy

```python
class ErrorHandler:
    def handle_pdf_error(self, error: Exception, filename: str) -> ProcessingResult
    def handle_llm_error(self, error: Exception, retry_count: int) -> bool
    def handle_validation_error(self, error: ValidationError) -> ProcessingResult
    def log_error(self, error: Exception, context: Dict[str, Any]) -> None
```

### Retry Logic

- **LLM API calls**: Exponential backoff with jitter (max 3 retries)
- **PDF processing**: Single retry with different extraction method
- **Date parsing**: Fallback to manual extraction patterns

## Testing Strategy

### Unit Testing

- **PDF Extraction**: Test with various PDF formats and corrupted files
- **Date Parsing**: Test with different date formats and edge cases
- **LLM Response Parsing**: Test with malformed and edge case responses
- **Career Analysis**: Test gap detection and job stability calculations

### Integration Testing

- **End-to-End Workflow**: Complete CV processing pipeline
- **API Integration**: Groq API interaction and error handling
- **File Upload**: Streamlit file handling and validation

### Performance Testing

- **Concurrent Processing**: Multiple CV analysis simultaneously
- **Large File Handling**: Processing large PDF files
- **Memory Usage**: Monitoring memory consumption during batch processing

### Test Data

- **Sample CVs**: Collection of anonymized CVs with various formats
- **Edge Cases**: CVs with missing dates, unusual formats, multiple languages
- **Error Scenarios**: Corrupted PDFs, network failures, API errors

## Security Considerations

### Data Privacy

- **File Handling**: Temporary file storage with automatic cleanup
- **API Keys**: Secure environment variable management
- **Logging**: Sanitized logs without personal information

### Input Validation

- **File Upload**: Size limits, format validation, malware scanning
- **Text Input**: XSS prevention, input sanitization
- **Configuration**: Parameter validation and bounds checking

## Performance Optimization

### Caching Strategy

- **PDF Text Extraction**: Cache extracted text to avoid reprocessing
- **LLM Responses**: Cache responses for identical inputs
- **Analysis Results**: Session-based result caching

### Async Processing

- **Concurrent CV Processing**: Process multiple CVs simultaneously
- **Non-blocking UI**: Maintain responsive interface during processing
- **Progress Tracking**: Real-time progress updates

### Resource Management

- **Memory Usage**: Efficient PDF processing and text handling
- **API Rate Limits**: Intelligent request throttling
- **File Cleanup**: Automatic temporary file removal

## Deployment Considerations

### Environment Setup

- **Dependencies**: Requirements.txt with pinned versions
- **Configuration**: Environment-specific settings
- **Logging**: Structured logging with appropriate levels

### Scalability

- **Horizontal Scaling**: Support for multiple worker processes
- **Load Balancing**: Distribution of processing load
- **Resource Monitoring**: CPU, memory, and API usage tracking