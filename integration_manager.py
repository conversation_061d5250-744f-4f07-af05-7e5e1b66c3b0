"""
Integration Manager for CV Analyzer Enhancement.

This module serves as the central integration point that wires all components together,
ensuring proper initialization, error handling, and component communication.
"""

import asyncio
import logging
import os
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

from models import (
    AnalysisConfig, EnhancedAssessment, ProcessingStatus, 
    BatchProcessingResult, RedFlag, RedFlagType, Severity
)
from analysis_engine import AnalysisEngine
from career_analyzer import CareerAnalyzer
from llm_service import EnhancedLLMService
from date_parser import DateParser
from error_handler import <PERSON>rrorHandler, ErrorCategory, ErrorSeverity
from session_manager import SessionStateManager
from progress_tracker import CVAnalysisProgressTracker, TaskStatus
from export_service import ExportService
from pdf_cache import PDFTextCache
from llm_cache import LLMResponseCache

logger = logging.getLogger(__name__)


class IntegrationManager:
    """
    Central integration manager that coordinates all system components.
    
    This class ensures proper initialization, dependency injection, and
    component communication throughout the CV analysis system.
    """
    
    def __init__(self):
        """Initialize the integration manager."""
        self.is_initialized = False
        self.components: Dict[str, Any] = {}
        self.error_handler: Optional[ErrorHandler] = None
        self.session_manager: Optional[SessionStateManager] = None
        
    def initialize_system(self, config: AnalysisConfig) -> bool:
        """
        Initialize all system components with proper dependency injection.

        Args:
            config: Analysis configuration

        Returns:
            True if initialization successful, False otherwise
        """
        try:
            logger.info("Starting system initialization...")

            # Load environment variables first
            load_dotenv()

            # Step 1: Initialize core infrastructure components
            self._initialize_infrastructure()
            
            # Step 2: Initialize caching systems
            self._initialize_caching_systems()
            
            # Step 3: Initialize analysis components
            self._initialize_analysis_components(config)
            
            # Step 4: Initialize UI and export components
            self._initialize_ui_components()
            
            # Step 5: Wire components together
            self._wire_components()
            
            # Step 6: Validate system integrity
            self._validate_system_integrity()
            
            self.is_initialized = True
            logger.info("System initialization completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            if self.error_handler:
                from error_handler import ErrorContext
                context = ErrorContext(operation="system_initialization")
                self.error_handler.handle_error(e, context, ErrorCategory.CONFIGURATION)
            return False
    
    def _initialize_infrastructure(self):
        """Initialize core infrastructure components."""
        logger.info("Initializing infrastructure components...")
        
        # Initialize error handler first
        self.error_handler = ErrorHandler()
        self.components['error_handler'] = self.error_handler
        
        # Initialize session manager
        self.session_manager = SessionStateManager()
        self.components['session_manager'] = self.session_manager
        
        # Set up logging integration
        self._setup_logging_integration()
        
    def _initialize_caching_systems(self):
        """Initialize caching systems."""
        logger.info("Initializing caching systems...")
        
        # Initialize PDF cache
        pdf_cache = PDFTextCache()
        self.components['pdf_cache'] = pdf_cache
        
        # Initialize LLM response cache
        llm_cache = LLMResponseCache()
        self.components['llm_cache'] = llm_cache
        
    def _initialize_analysis_components(self, config: AnalysisConfig):
        """Initialize analysis components."""
        logger.info("Initializing analysis components...")
        
        # Get API key
        api_key = os.getenv('GROQ_API_KEY')
        if not api_key:
            raise ValueError("GROQ_API_KEY environment variable not set")
        
        # Initialize date parser
        date_parser = DateParser()
        self.components['date_parser'] = date_parser
        
        # Initialize career analyzer
        career_analyzer = CareerAnalyzer(config)
        self.components['career_analyzer'] = career_analyzer
        
        # Initialize LLM service with cache integration
        llm_service = EnhancedLLMService(
            api_key=api_key,
            temperature=config.llm_temperature,
            max_tokens=config.llm_max_tokens,
            max_retries=config.max_retries,
            timeout=config.timeout_seconds,
            enable_caching=True,
            cache_expiry_hours=24
        )
        self.components['llm_service'] = llm_service
        
        # Initialize main analysis engine
        analysis_engine = AnalysisEngine(config, api_key)
        self.components['analysis_engine'] = analysis_engine
        
    def _initialize_ui_components(self):
        """Initialize UI and export components."""
        logger.info("Initializing UI components...")
        
        # Initialize export service
        export_service = ExportService()
        self.components['export_service'] = export_service
        
    def _wire_components(self):
        """Wire components together with proper dependency injection."""
        logger.info("Wiring components together...")
        
        # Wire error handler into all components
        for component_name, component in self.components.items():
            if hasattr(component, 'set_error_handler'):
                component.set_error_handler(self.error_handler)
        
        # Wire session manager into components that need it
        session_dependent_components = ['analysis_engine', 'export_service']
        for component_name in session_dependent_components:
            if component_name in self.components:
                component = self.components[component_name]
                if hasattr(component, 'set_session_manager'):
                    component.set_session_manager(self.session_manager)
        
        # LLM service manages its own cache internally
        
        # Wire PDF cache into analysis engine
        analysis_engine = self.components.get('analysis_engine')
        if analysis_engine and hasattr(analysis_engine, 'set_pdf_cache'):
            analysis_engine.set_pdf_cache(self.components['pdf_cache'])
    
    def _validate_system_integrity(self):
        """Validate that all components are properly initialized and connected."""
        logger.info("Validating system integrity...")
        
        required_components = [
            'error_handler', 'session_manager', 'pdf_cache', 'llm_cache',
            'date_parser', 'career_analyzer', 'llm_service', 'analysis_engine',
            'export_service'
        ]
        
        missing_components = []
        for component_name in required_components:
            if component_name not in self.components:
                missing_components.append(component_name)
        
        if missing_components:
            raise RuntimeError(f"Missing required components: {missing_components}")
        
        # Test component connectivity
        self._test_component_connectivity()
        
    def _test_component_connectivity(self):
        """Test that components can communicate properly."""
        logger.info("Testing component connectivity...")
        
        # Test error handler integration
        try:
            from error_handler import ErrorContext
            test_error = ValueError("Test error for connectivity check")
            context = ErrorContext(operation="connectivity_test")
            self.error_handler.handle_error(test_error, context, ErrorCategory.UNKNOWN)
        except Exception as e:
            raise RuntimeError(f"Error handler connectivity test failed: {e}")
        
        # Test session manager integration
        try:
            # Test basic functionality - just check if it exists
            if self.session_manager is None:
                raise RuntimeError("Session manager is None")
        except Exception as e:
            raise RuntimeError(f"Session manager connectivity test failed: {e}")
        
    def _setup_logging_integration(self):
        """Set up integrated logging across all components."""
        # Configure logging to work with error handler
        # Note: Disabled to prevent infinite recursion
        pass
    
    async def run_integrated_analysis(
        self,
        cv_files: List[Dict[str, Any]],
        job_description: str,
        config: AnalysisConfig,
        progress_callback: Optional[Callable] = None
    ) -> BatchProcessingResult:
        """
        Run integrated CV analysis using all system components.
        
        Args:
            cv_files: List of CV file data
            job_description: Job description text
            config: Analysis configuration
            progress_callback: Optional progress callback
            
        Returns:
            Batch processing result
        """
        if not self.is_initialized:
            raise RuntimeError("System not initialized. Call initialize_system() first.")
        
        logger.info(f"Starting integrated analysis of {len(cv_files)} CVs")
        
        try:
            # Initialize progress tracking
            progress_tracker = CVAnalysisProgressTracker(len(cv_files))
            if progress_callback:
                progress_tracker.add_progress_callback(progress_callback)
            
            # Start session tracking
            session_id = self.session_manager.create_new_session(
                job_description, config, [f['name'] for f in cv_files]
            )
            
            # Run analysis through the analysis engine
            analysis_engine = self.components['analysis_engine']
            result = await analysis_engine.analyze_candidates(
                cv_files, job_description, progress_callback
            )
            
            # Update session with results
            self.session_manager.update_session_results(
                session_id, result.assessments, result.processing_time_seconds
            )
            
            # Generate comprehensive analysis report
            enhanced_result = self._enhance_batch_result(result, config)
            
            logger.info(f"Integrated analysis completed: {result.successful_count} successful")
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Integrated analysis failed: {e}")
            from error_handler import ErrorContext
            context = ErrorContext(operation="integrated_analysis")
            self.error_handler.handle_error(e, context, ErrorCategory.ANALYSIS_ERROR)
            raise
    
    def _enhance_batch_result(
        self, 
        result: BatchProcessingResult, 
        config: AnalysisConfig
    ) -> BatchProcessingResult:
        """Enhance batch result with additional analysis and insights."""
        
        # Add system-level red flags
        system_red_flags = self._generate_system_red_flags(result, config)
        
        # Add batch-level insights
        batch_insights = self._generate_batch_insights(result)
        
        # Enhance individual assessments
        for assessment in result.assessments:
            if assessment.processing_status == ProcessingStatus.COMPLETED:
                self._enhance_individual_assessment(assessment, config)
        
        # Note: Additional metadata could be added here if the model supports it
        
        return result
    
    def _generate_system_red_flags(
        self, 
        result: BatchProcessingResult, 
        config: AnalysisConfig
    ) -> List[RedFlag]:
        """Generate system-level red flags based on batch analysis."""
        red_flags = []
        
        # Check for overall low match scores
        successful_assessments = [
            a for a in result.assessments 
            if a.processing_status == ProcessingStatus.COMPLETED
        ]
        
        if successful_assessments:
            avg_match_score = sum(a.match_score for a in successful_assessments) / len(successful_assessments)
            
            if avg_match_score < 0.3:
                red_flags.append(RedFlag(
                    type=RedFlagType.SKILL_MISMATCH,
                    severity=Severity.HIGH,
                    description=f"Overall low match scores (avg: {avg_match_score:.2f}). Consider reviewing job requirements.",
                    details={"average_match_score": avg_match_score}
                ))
        
        # Check for high failure rate
        if result.failed_count > result.successful_count:
            red_flags.append(RedFlag(
                type=RedFlagType.MISSING_REQUIRED_SKILL,
                severity=Severity.CRITICAL,
                description=f"High analysis failure rate: {result.failed_count}/{result.total_processed} failed",
                details={"failure_rate": result.failed_count / result.total_processed}
            ))
        
        return red_flags
    
    def _generate_batch_insights(self, result: BatchProcessingResult) -> Dict[str, Any]:
        """Generate insights from batch processing results."""
        insights = {
            "processing_efficiency": {
                "total_time": result.processing_time_seconds,
                "avg_time_per_cv": result.processing_time_seconds / max(1, result.total_processed),
                "success_rate": result.successful_count / max(1, result.total_processed)
            },
            "candidate_distribution": {},
            "skill_analysis": {},
            "recommendations": []
        }
        
        successful_assessments = [
            a for a in result.assessments 
            if a.processing_status == ProcessingStatus.COMPLETED
        ]
        
        if successful_assessments:
            # Match score distribution
            match_scores = [a.match_score for a in successful_assessments]
            insights["candidate_distribution"] = {
                "high_match": len([s for s in match_scores if s >= 0.8]),
                "medium_match": len([s for s in match_scores if 0.5 <= s < 0.8]),
                "low_match": len([s for s in match_scores if s < 0.5])
            }
            
            # Generate recommendations
            if insights["candidate_distribution"]["high_match"] == 0:
                insights["recommendations"].append(
                    "No high-match candidates found. Consider broadening job requirements."
                )
            
            if len([a for a in successful_assessments if a.employment_gaps]) > len(successful_assessments) * 0.5:
                insights["recommendations"].append(
                    "Many candidates have employment gaps. Consider if this is acceptable for the role."
                )
        
        return insights
    
    def _enhance_individual_assessment(
        self, 
        assessment: EnhancedAssessment, 
        config: AnalysisConfig
    ):
        """Enhance individual assessment with additional analysis."""
        
        # Add confidence scoring
        if not hasattr(assessment, 'confidence_score'):
            assessment.confidence_score = self._calculate_confidence_score(assessment)
        
        # Note: Additional enhancements could be added here if the model supports them
    
    def _calculate_confidence_score(self, assessment: EnhancedAssessment) -> float:
        """Calculate confidence score for an assessment."""
        confidence_factors = []
        
        # Data completeness factor
        completeness = 0.0
        if assessment.candidate_name: completeness += 0.2
        if assessment.email: completeness += 0.2
        if assessment.positions: completeness += 0.3
        if assessment.experience_years > 0: completeness += 0.3
        confidence_factors.append(completeness)
        
        # Analysis quality factor
        if assessment.positions:
            positions_with_dates = sum(1 for p in assessment.positions if p.start_date)
            date_quality = positions_with_dates / len(assessment.positions)
            confidence_factors.append(date_quality)
        
        # Error factor (inverse)
        error_factor = max(0.0, 1.0 - len(assessment.error_messages) * 0.2)
        confidence_factors.append(error_factor)
        
        return sum(confidence_factors) / len(confidence_factors) if confidence_factors else 0.5
    
    def _generate_recommendation_tags(
        self, 
        assessment: EnhancedAssessment, 
        config: AnalysisConfig
    ) -> List[str]:
        """Generate recommendation tags for an assessment."""
        tags = []
        
        if assessment.match_score >= 0.8:
            tags.append("high_match")
        elif assessment.match_score >= 0.5:
            tags.append("medium_match")
        else:
            tags.append("low_match")
        
        if assessment.experience_years >= 5:
            tags.append("experienced")
        elif assessment.experience_years >= 2:
            tags.append("mid_level")
        else:
            tags.append("junior")
        
        if assessment.employment_gaps:
            tags.append("has_gaps")
        
        if any(flag.severity in [Severity.HIGH, Severity.CRITICAL] for flag in assessment.red_flags):
            tags.append("has_concerns")
        
        return tags
    
    def _calculate_priority_score(
        self, 
        assessment: EnhancedAssessment, 
        config: AnalysisConfig
    ) -> float:
        """Calculate priority score for candidate ranking."""
        score = assessment.match_score * 0.4  # Base match score (40%)
        
        # Experience bonus (20%)
        if assessment.experience_years >= 5:
            score += 0.2
        elif assessment.experience_years >= 2:
            score += 0.1
        
        # Stability bonus (20%)
        if hasattr(assessment, 'job_stability_analysis') and assessment.job_stability_analysis:
            score += assessment.job_stability_analysis.stability_score * 0.2
        
        # Red flag penalty (20%)
        red_flag_penalty = 0.0
        for flag in assessment.red_flags:
            if flag.severity == Severity.CRITICAL:
                red_flag_penalty += 0.1
            elif flag.severity == Severity.HIGH:
                red_flag_penalty += 0.05
            elif flag.severity == Severity.MEDIUM:
                red_flag_penalty += 0.02
        
        score = max(0.0, score - red_flag_penalty)
        
        return min(1.0, score)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        status = {
            "initialized": self.is_initialized,
            "components": {},
            "health_check": {},
            "performance_metrics": {}
        }
        
        # Component status
        for name, component in self.components.items():
            status["components"][name] = {
                "loaded": component is not None,
                "type": type(component).__name__
            }
        
        # Health checks
        if self.is_initialized:
            status["health_check"] = self._run_health_checks()
        
        # Performance metrics
        if self.session_manager:
            # Get basic session info instead of performance metrics
            status["performance_metrics"] = {
                "session_manager_available": True
            }
        
        return status
    
    def _run_health_checks(self) -> Dict[str, bool]:
        """Run health checks on all components."""
        health_status = {}
        
        # Check API connectivity
        try:
            api_key = os.getenv('GROQ_API_KEY')
            health_status["api_key_configured"] = bool(api_key)
        except Exception:
            health_status["api_key_configured"] = False
        
        # Check cache systems
        try:
            pdf_cache = self.components.get('pdf_cache')
            health_status["pdf_cache_healthy"] = pdf_cache is not None
        except Exception:
            health_status["pdf_cache_healthy"] = False
        
        try:
            llm_cache = self.components.get('llm_cache')
            health_status["llm_cache_healthy"] = llm_cache is not None
        except Exception:
            health_status["llm_cache_healthy"] = False
        
        # Check session manager
        try:
            health_status["session_manager_healthy"] = (
                self.session_manager is not None and 
                hasattr(self.session_manager, 'get_current_session_id')
            )
        except Exception:
            health_status["session_manager_healthy"] = False
        
        return health_status
    
    def shutdown_system(self):
        """Gracefully shutdown all system components."""
        logger.info("Shutting down system...")
        
        # Cleanup caches
        if 'pdf_cache' in self.components:
            self.components['pdf_cache'].cleanup()
        
        if 'llm_cache' in self.components:
            self.components['llm_cache'].cleanup()
        
        # Save session state
        if self.session_manager:
            self.session_manager.save_session_state()
        
        # Clear components
        self.components.clear()
        self.is_initialized = False
        
        logger.info("System shutdown completed")


class ErrorHandlerLogHandler(logging.Handler):
    """Custom log handler that integrates with the error handler."""
    
    def __init__(self, error_handler: ErrorHandler):
        super().__init__()
        self.error_handler = error_handler
    
    def emit(self, record):
        """Emit log record to error handler if it's an error or critical level."""
        if record.levelno >= logging.ERROR:
            try:
                # Convert log record to error for error handler
                from error_handler import ErrorContext
                error = RuntimeError(record.getMessage())
                context = ErrorContext(operation=record.name)
                self.error_handler.handle_error(error, context, ErrorCategory.UNKNOWN)
            except Exception:
                # Don't let error handler errors break logging
                pass


# Global integration manager instance
integration_manager = IntegrationManager()


def get_integration_manager() -> IntegrationManager:
    """Get the global integration manager instance."""
    return integration_manager


def initialize_integrated_system(config: AnalysisConfig) -> bool:
    """Initialize the integrated system with the given configuration."""
    return integration_manager.initialize_system(config)


async def run_integrated_cv_analysis(
    cv_files: List[Dict[str, Any]],
    job_description: str,
    config: AnalysisConfig,
    progress_callback: Optional[Callable] = None
) -> BatchProcessingResult:
    """Run integrated CV analysis using the global integration manager."""
    return await integration_manager.run_integrated_analysis(
        cv_files, job_description, config, progress_callback
    )