#!/usr/bin/env python3
"""
Comprehensive validation test for the CV Analyzer project.
Tests the complete workflow from PDF upload to analysis results.
"""

import asyncio
import os
import sys
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from models import AnalysisConfig, ProcessingStatus
from integration_manager import get_integration_manager, initialize_integrated_system
from export_service import ExportService
from PyPDF2 import PdfReader
import io

class CVAnalyzerValidator:
    """Comprehensive validator for the CV Analyzer system."""
    
    def __init__(self):
        self.integration_mgr = None
        self.test_results = []
        
    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results."""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        
        self.test_results.append({
            'test': test_name,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
    
    def test_system_initialization(self) -> bool:
        """Test system initialization."""
        print("\n🔧 Testing System Initialization...")
        
        try:
            # Test integration manager import
            self.integration_mgr = get_integration_manager()
            self.log_test("Integration Manager Import", True)
            
            # Test system initialization
            config = AnalysisConfig()
            result = initialize_integrated_system(config)
            self.log_test("System Initialization", result, 
                         f"Initialized: {self.integration_mgr.is_initialized}")
            
            if result:
                # Test system status
                status = self.integration_mgr.get_system_status()
                components_loaded = len(status.get('components', {}))
                self.log_test("Component Loading", components_loaded > 0, 
                             f"Loaded {components_loaded} components")
                
                # Test health checks
                health_checks = status.get('health_check', {})
                api_configured = health_checks.get('api_key_configured', False)
                self.log_test("API Key Configuration", api_configured)
                
            return result
            
        except Exception as e:
            self.log_test("System Initialization", False, f"Error: {e}")
            return False
    
    def test_pdf_processing(self) -> bool:
        """Test PDF text extraction."""
        print("\n📄 Testing PDF Processing...")
        
        try:
            test_files = list(Path('test_resumes').glob('*.pdf'))

            if not test_files:
                self.log_test("PDF Files Available", False, "No test PDF files found")
                return False

            self.log_test("PDF Files Available", True, f"Found {len(test_files)} test files")

            # Test each PDF file
            successful_extractions = 0
            for pdf_file in test_files:
                try:
                    with open(pdf_file, 'rb') as f:
                        pdf_content = f.read()

                    # Extract text using PyPDF2
                    pdf_reader = PdfReader(io.BytesIO(pdf_content))
                    extracted_text = ""
                    for page in pdf_reader.pages:
                        extracted_text += page.extract_text() + "\n"

                    if extracted_text and len(extracted_text.strip()) > 100:
                        successful_extractions += 1
                        self.log_test(f"PDF Extraction - {pdf_file.name}", True,
                                     f"Extracted {len(extracted_text)} characters")
                    else:
                        self.log_test(f"PDF Extraction - {pdf_file.name}", False,
                                     "Insufficient text extracted")

                except Exception as e:
                    self.log_test(f"PDF Extraction - {pdf_file.name}", False, f"Error: {e}")
            
            overall_success = successful_extractions == len(test_files)
            self.log_test("Overall PDF Processing", overall_success, 
                         f"{successful_extractions}/{len(test_files)} successful")
            
            return overall_success
            
        except Exception as e:
            self.log_test("PDF Processing Setup", False, f"Error: {e}")
            return False
    
    def prepare_cv_data(self) -> List[Dict[str, Any]]:
        """Prepare CV data for analysis."""
        cv_data = []

        test_files = list(Path('test_resumes').glob('*.pdf'))

        for pdf_file in test_files:
            try:
                with open(pdf_file, 'rb') as f:
                    pdf_content = f.read()

                # Extract text using PyPDF2
                pdf_reader = PdfReader(io.BytesIO(pdf_content))
                extracted_text = ""
                for page in pdf_reader.pages:
                    extracted_text += page.extract_text() + "\n"

                cv_data.append({
                    'name': pdf_file.name,
                    'content': extracted_text,
                    'size': len(pdf_content),
                    'type': 'pdf'
                })

            except Exception as e:
                print(f"⚠️  Failed to prepare {pdf_file.name}: {e}")

        return cv_data
    
    async def test_cv_analysis(self) -> bool:
        """Test the complete CV analysis workflow."""
        print("\n🧠 Testing CV Analysis Workflow...")
        
        if not self.integration_mgr or not self.integration_mgr.is_initialized:
            self.log_test("Analysis Prerequisites", False, "System not initialized")
            return False
        
        try:
            # Prepare test data
            cv_data = self.prepare_cv_data()
            if not cv_data:
                self.log_test("CV Data Preparation", False, "No CV data available")
                return False
            
            self.log_test("CV Data Preparation", True, f"Prepared {len(cv_data)} CVs")
            
            # Define job description
            job_description = """
            Senior Python Developer Position
            
            We are seeking an experienced Python developer to join our dynamic team.
            
            Required Skills:
            - Python programming (3+ years experience)
            - Django or Flask web framework
            - Database experience (PostgreSQL, MySQL)
            - Team leadership and mentoring abilities
            - Experience with cloud platforms (AWS preferred)
            
            Preferred Skills:
            - Docker and containerization
            - CI/CD pipeline experience
            - Microservices architecture
            - Agile development methodology
            
            Responsibilities:
            - Lead development of web applications
            - Mentor junior developers
            - Design scalable system architecture
            - Collaborate with cross-functional teams
            """
            
            # Run analysis
            config = AnalysisConfig()
            
            print("   Running integrated analysis...")
            result = await self.integration_mgr.run_integrated_analysis(
                cv_data, job_description, config
            )
            
            # Validate results
            self.log_test("Analysis Execution", True, 
                         f"Processed {result.total_processed} CVs")
            
            self.log_test("Successful Processing", result.successful_count > 0,
                         f"{result.successful_count}/{result.total_processed} successful")
            
            if result.failed_count > 0:
                self.log_test("Failed Processing", False,
                             f"{result.failed_count} CVs failed processing")
            
            # Analyze individual results
            if result.assessments:
                for i, assessment in enumerate(result.assessments):
                    cv_name = cv_data[i]['name'] if i < len(cv_data) else f"CV_{i}"
                    
                    if assessment.processing_status == ProcessingStatus.COMPLETED:
                        self.log_test(f"Assessment - {cv_name}", True,
                                     f"Match: {assessment.match_score:.1%}, "
                                     f"Red flags: {len(assessment.red_flags)}")
                    else:
                        self.log_test(f"Assessment - {cv_name}", False,
                                     f"Status: {assessment.processing_status}")
            
            return result.successful_count > 0
            
        except Exception as e:
            self.log_test("CV Analysis", False, f"Error: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_export_functionality(self) -> bool:
        """Test export functionality."""
        print("\n📊 Testing Export Functionality...")
        
        try:
            export_service = ExportService()
            self.log_test("Export Service Creation", True)
            
            # Test export methods exist
            methods = ['export_to_excel', 'export_to_csv', 'export_to_json']
            for method in methods:
                has_method = hasattr(export_service, method)
                self.log_test(f"Export Method - {method}", has_method)
            
            return True
            
        except Exception as e:
            self.log_test("Export Functionality", False, f"Error: {e}")
            return False
    
    async def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run comprehensive validation of the CV Analyzer."""
        print("🚀 Starting Comprehensive CV Analyzer Validation")
        print("=" * 60)
        
        # Test sequence
        tests = [
            ("System Initialization", self.test_system_initialization),
            ("PDF Processing", self.test_pdf_processing),
            ("CV Analysis", self.test_cv_analysis),
            ("Export Functionality", self.test_export_functionality),
        ]
        
        results = {}
        overall_success = True
        
        for test_name, test_func in tests:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    success = await test_func()
                else:
                    success = test_func()
                
                results[test_name] = success
                if not success:
                    overall_success = False
                    
            except Exception as e:
                print(f"❌ {test_name} failed with exception: {e}")
                results[test_name] = False
                overall_success = False
        
        # Summary
        print("\n" + "=" * 60)
        print("🎯 VALIDATION SUMMARY")
        print("=" * 60)
        
        passed_tests = sum(1 for result in self.test_results if result['success'])
        total_tests = len(self.test_results)
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")
        
        if overall_success:
            print("\n✅ CV ANALYZER VALIDATION: PASSED")
            print("🎉 The CV Analyzer is working correctly!")
        else:
            print("\n❌ CV ANALYZER VALIDATION: FAILED")
            print("⚠️  Some components need attention.")
        
        # Save detailed results
        validation_report = {
            'timestamp': datetime.now().isoformat(),
            'overall_success': overall_success,
            'test_summary': results,
            'detailed_results': self.test_results,
            'stats': {
                'total_tests': total_tests,
                'passed': passed_tests,
                'failed': total_tests - passed_tests,
                'success_rate': passed_tests/total_tests*100
            }
        }
        
        with open('validation_report.json', 'w') as f:
            json.dump(validation_report, f, indent=2)
        
        print(f"\n📋 Detailed report saved to: validation_report.json")
        
        return validation_report

async def main():
    """Main validation function."""
    validator = CVAnalyzerValidator()
    await validator.run_comprehensive_validation()

if __name__ == "__main__":
    asyncio.run(main())
