# Streamlit Secrets Configuration
# This file should contain your actual API keys and sensitive configuration
# Copy this file and rename to secrets.toml, then fill in your actual values

[groq]
# Groq API configuration
api_key = "your_groq_api_key_here"
model = "llama3-70b-8192"
max_tokens = 4000
temperature = 0.1

[analysis]
# Analysis configuration
max_concurrent_requests = 5
request_timeout = 30
retry_attempts = 3

[logging]
# Logging configuration
log_level = "INFO"
enable_performance_logging = true

[cache]
# Caching configuration
enable_pdf_cache = true
enable_llm_cache = true
cache_ttl_hours = 24

# Example of how to use environment variables instead:
# [groq]
# api_key = "$GROQ_API_KEY"