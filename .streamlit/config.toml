[global]
# Development mode settings
developmentMode = false
showWarningOnDirectExecution = false

[server]
# Server configuration
port = 8501
address = "0.0.0.0"
baseUrlPath = ""
enableCORS = false
enableXsrfProtection = true
maxUploadSize = 200
maxMessageSize = 200
enableWebsocketCompression = false

[browser]
# Browser settings
gatherUsageStats = false
serverAddress = "localhost"
serverPort = 8501

[theme]
# UI theme configuration
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
font = "sans serif"

[client]
# Client configuration
caching = true
displayEnabled = true
showErrorDetails = true

[runner]
# Runner configuration
magicEnabled = true
installTracer = false
fixMatplotlib = true
postScriptGC = true
fastReruns = true
enforceSerializableSessionState = false

[logger]
# Logging configuration
level = "info"
messageFormat = "%(asctime)s %(message)s"

[deprecation]
# Deprecation warnings
showPyplotGlobalUse = false